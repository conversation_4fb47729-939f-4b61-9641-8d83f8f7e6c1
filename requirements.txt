# 数据分析和科学计算库
pandas>=2.0.0
numpy>=1.24.0
matplotlib>=3.6.0
#duckdb>=0.8.0
scipy>=1.10.0
scikit-learn>=1.3.0

# Web和API相关
requests>=2.28.0
urllib3>=1.26.0

# 绘图和可视化
plotly>=5.14.0

# 流程图支持（可选，用于生成Mermaid图表）
# 注意：Mermaid图表主要在markdown中渲染，不需要额外的Python包
# 如果需要在Python中生成Mermaid代码，可以考虑：
# mermaid-py>=0.3.0

# Jupyter/IPython环境
ipython>=8.10.0
jupyter>=1.0.0

# AI/LLM相关
openai>=1.0.0
pyyaml>=6.0

# 配置管理
python-dotenv>=1.0.0

# 异步编程
asyncio-mqtt>=0.11.1

# 文档生成（基于输出的Word文档）
python-docx>=0.8.11

# 系统和工具库
pathlib2>=2.3.7
typing-extensions>=4.5.0

# 开发和测试工具（可选）
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# 字体支持（用于matplotlib中文显示）
fonttools>=4.38.0
