from data_analysis_agent import DataAnalysisAgent
from config.llm_config import LLMConfig

def main():
    llm_config = LLMConfig()
    agent = DataAnalysisAgent(llm_config)
    files = ["./Guizhou Maotai Income Statement.csv"]
    report = agent.analyze(user_input="Based on <PERSON><PERSON><PERSON>'s data, output five important statistical indicators and create relevant charts. Finally, generate a report for me.",files=files)
    print(report)
    
if __name__ == "__main__":
    main()
    